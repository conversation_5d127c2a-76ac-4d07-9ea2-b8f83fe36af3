import { PayrixService } from "../../../service/payrix.service.js";
import { logger } from "../../../helpers/logger.js";
import type { OnboardingRequest } from "../schemas/onboarding.schema.js";

export interface UserAccountResult {
  success: boolean;
  data?: {
    id: string;
    email: string;
    sanitizedUsername?: string;
    originalUsername?: string;
    [key: string]: unknown;
  };
  error?: string;
}

export async function createUserAccountIfRequested(data: OnboardingRequest, payrixEntityId: string, requestId: string): Promise<UserAccountResult> {
  if (!data.userAccount?.createAccount || !data.userAccount?.username || !data.userAccount?.password) {
    return { success: false, error: "Username and password are required for account creation" };
  }

  try {
    logger.info("Creating user account for merchant", {
      requestId,
      payrixEntityId,
      username: data.userAccount.username,
      hasMerchantData: !!data.merchant,
      membersCount: data.merchant?.members?.length || 0,
    });

    // Fix: Members are nested under data.merchant.members, not data.members
    const members = data.merchant?.members;
    if (!members || members.length === 0) {
      throw new Error("No members found in merchant data to create user account");
    }

    // Find primary member (primary === "1" as string) or fall back to first member
    const primaryMember = members.find((member) => member.primary === "1") || members[0];

    if (!primaryMember) {
      throw new Error("No primary member found to create user account");
    }

    logger.info("Found primary member for user account creation", {
      requestId,
      memberEmail: primaryMember.email,
      memberName: `${primaryMember.first} ${primaryMember.last}`,
      isPrimary: primaryMember.primary === "1",
    });

    const payrixService = new PayrixService();
    const userAccountData = await payrixService.createUserAccount({
      username: data.userAccount.username,
      password: data.userAccount.password,
      first: primaryMember.first,
      last: primaryMember.last,
      email: primaryMember.email,
      merchantId: payrixEntityId,
      // Required Payrix API fields with sensible defaults
      // Note: partition defaults to authenticated user's partition if not specified
      partition: process.env.PAYRIX_PARTITION || "",
      roles: 128, // Merchant role (128) as per API documentation
      allowedResources: JSON.stringify({
        create: ["accounts", "payouts"],
        read: ["disbursements", "txns", "entities"],
        update: ["accounts"],
        delete: [],
        totals: ["disbursements"],
      }),
      restrictedResources: JSON.stringify({
        create: [],
        read: [],
        update: [],
        delete: [],
        totals: [],
      }),
      portalAccess: 1, // Enable portal access
      inactive: 0,
      frozen: 0,
      mfaEnabled: 0,
    });

    logger.info("User account created successfully", {
      requestId,
      payrixEntityId,
      username: data.userAccount.username,
      userAccountId: userAccountData?.id,
    });

    return {
      success: true,
      data: userAccountData,
    };
  } catch (userError) {
    logger.error("User account creation failed", {
      requestId,
      payrixEntityId,
      username: data.userAccount?.username,
      error: (userError as Error).message,
    });

    return {
      success: false,
      error: (userError as Error).message,
    };
  }
}

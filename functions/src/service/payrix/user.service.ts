import { AxiosError, AxiosInstance } from "axios";
import { logger } from "../../helpers/logger.js";
import { PayrixUserResponse, PayrixError, UserAccountData } from "../../types/payrix.types.js";
import { createPayrixApiClient } from "./api-client.js";

export class PayrixUserService {
  private apiClient: AxiosInstance;

  constructor() {
    this.apiClient = createPayrixApiClient();
  }

  async createUserAccount(userData: UserAccountData): Promise<PayrixUserResponse> {
    try {
      logger.info("Creating user account in Payrix", {
        originalUsername: userData.username,
        email: userData.email,
        merchantId: userData.merchantId,
        hasPartition: !!userData.partition,
        hasRoles: !!userData.roles,
        hasAllowedResources: !!userData.allowedResources,
        hasRestrictedResources: !!userData.restrictedResources,
        portalAccess: userData.portalAccess,
      });

      // Log the full payload (excluding sensitive data) for debugging
      const debugPayload = { ...userData };
      delete debugPayload.password; // Remove password from logs
      logger.info("User account creation payload", { payload: debugPayload });

      const response = await this.apiClient.post("/logins", userData);

      logger.info("Payrix user account API response", {
        status: response.status,
        username: userData.username,
        data: response.data,
      });

      const errors: PayrixError[] = response.data?.response?.errors || [];
      if (errors.length > 0) {
        const errorMessages = errors.map((err: PayrixError) => `${err.field}: ${err.msg}`).join(", ");
        logger.error("Payrix API returned validation errors", {
          originalUsername: userData.username,
          email: userData.email,
          errors: errors,
        });
        throw new Error(`Payrix validation errors: ${errorMessages}`);
      }

      const loginData = response.data?.response?.data?.[0];
      if (!loginData) {
        logger.error("Invalid Payrix response structure: no login data found", {
          originalUsername: userData.username,
          email: userData.email,
        });
        throw new Error("Invalid Payrix response structure: no login data found");
      }

      logger.info("Payrix user account created successfully", {
        status: response.status,
        originalUsername: userData.username,
        loginId: loginData.id,
      });

      return {
        ...loginData,
        originalUsername: userData.username,
      };
    } catch (error) {
      const axiosError = error as AxiosError;

      logger.error("Payrix User Account Creation Error", {
        originalUsername: userData.username,
        email: userData.email,
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
        message: axiosError.message,
      });

      throw new Error(
        `Payrix User Account Creation Error (${axiosError.response?.status}): ${JSON.stringify(axiosError.response?.data || axiosError.message)}`
      );
    }
  }
}

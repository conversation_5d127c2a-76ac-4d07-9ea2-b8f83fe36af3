export const API_DOCUMENTATION = `
# API Documentation

## Generate Integration Token

**Endpoint:** \`POST /payments/generate-integration-token\`

**Description:** Generates a secure token for iframe payment integration.

**Request Body:**
\`\`\`json
{
  "merchantId": "string (required)",
  "description": "string (required)",
  "amount": "number (optional, in cents)",
  "returnUrl": "string (optional)",
  "expiresIn": "number (optional, minutes, default: 60)"
}
\`\`\`

**Response:**
\`\`\`json
{
  "success": true,
  "message": "Integration token generated successfully",
  "data": {
    "token": "secure-token-string",
    "expiresAt": "2024-01-01T12:00:00.000Z",
    "embedUrl": "https://your-domain.com/payment-iframe?token=...",
    "merchantInfo": {
      "id": "merchant-id",
      "name": "Merchant Name",
      "status": 1
    }
  }
}
\`\`\`

## Validate Iframe Token

**Endpoint:** \`POST /payments/validate-iframe-token\`

**Description:** Validates a token and returns payment configuration for iframe.

**Request Body:**
\`\`\`json
{
  "token": "string (required)"
}
\`\`\`

**Response:**
\`\`\`json
{
  "success": true,
  "message": "Token validated successfully",
  "data": {
    "config": {
      "merchantId": "merchant-id",
      "publicKey": "payrix-public-key",
      "amount": 2500,
      "description": "Product Purchase",
      "mode": "txn",
      "txnType": "sale"
    },
    "merchantInfo": {
      "id": "merchant-id",
      "name": "Merchant Name",
      "status": 1
    },
    "paymentInfo": {
      "description": "Product Purchase",
      "amount": 2500,
      "returnUrl": "https://yoursite.com/success"
    }
  }
}
\`\`\`

## Get Iframe Configuration

**Endpoint:** \`GET /payments/iframe-config\`

**Description:** Gets iframe-specific configuration and styling options.

**Query Parameters:**
- \`domain\` (optional): Your domain for CORS configuration
- \`theme\` (optional): 'light', 'dark', or 'auto'
- \`language\` (optional): Language code (default: 'en')
- \`currency\` (optional): Currency code (default: 'USD')

**Response:**
\`\`\`json
{
  "success": true,
  "data": {
    "payrixConfig": {
      "scriptUrl": "https://test-api.payrix.com/payFieldsScript?spa=1&iframe=1",
      "environment": "test",
      "supportedFeatures": ["iframe-embedding", "auto-resize", "responsive-design"]
    },
    "styling": {
      "theme": "light",
      "customCSS": { /* Theme-specific styles */ }
    },
    "security": {
      "allowedOrigins": ["*"],
      "cspDirectives": ["frame-ancestors *", "script-src 'self' 'unsafe-inline'"]
    },
    "features": {
      "autoResize": true,
      "responsiveDesign": true,
      "mobileOptimized": true
    }
  }
}
\`\`\`

## Check Token Status

**Endpoint:** \`GET /payments/token-status?token=TOKEN\`

**Description:** Checks the status and validity of a payment token.

**Response:**
\`\`\`json
{
  "success": true,
  "data": {
    "isValid": true,
    "status": "valid",
    "expiresAt": "2024-01-01T12:00:00.000Z",
    "timeRemaining": 3600,
    "merchantId": "merchant-id",
    "amount": 2500,
    "description": "Product Purchase"
  }
}
\`\`\`

## Iframe Events

The payment iframe communicates with the parent window using \`postMessage\`. Listen for these events:

### PAYMENT_IFRAME_READY
Fired when the iframe has loaded and is ready to accept payments.
\`\`\`javascript
{
  type: 'PAYMENT_IFRAME_READY',
  data: {
    merchantName: 'Merchant Name',
    amount: 2500,
    description: 'Product Purchase'
  }
}
\`\`\`

### PAYFIELDS_SCRIPT_LOADED
Fired when the Payrix PayFields script has loaded successfully.
\`\`\`javascript
{
  type: 'PAYFIELDS_SCRIPT_LOADED',
  timestamp: '2024-01-01T12:00:00.000Z'
}
\`\`\`

### PAYMENT_SUBMISSION_STARTED
Fired when payment submission begins.
\`\`\`javascript
{
  type: 'PAYMENT_SUBMISSION_STARTED',
  timestamp: '2024-01-01T12:00:00.000Z'
}
\`\`\`

### PAYMENT_SUCCESS
Fired when a payment is successfully processed.
\`\`\`javascript
{
  type: 'PAYMENT_SUCCESS',
  data: {
    // Payment response data from Payrix
    transactionId: 'txn_123456',
    amount: 2500,
    status: 'approved'
  },
  timestamp: '2024-01-01T12:00:00.000Z'
}
\`\`\`

### PAYMENT_FAILURE
Fired when a payment fails.
\`\`\`javascript
{
  type: 'PAYMENT_FAILURE',
  error: 'Payment declined',
  details: { /* Error details */ },
  timestamp: '2024-01-01T12:00:00.000Z'
}
\`\`\`

### PAYMENT_VALIDATION_FAILURE
Fired when payment validation fails (e.g., invalid card details).
\`\`\`javascript
{
  type: 'PAYMENT_VALIDATION_FAILURE',
  error: 'Invalid card number',
  details: { /* Validation error details */ },
  timestamp: '2024-01-01T12:00:00.000Z'
}
\`\`\`

### PAYMENT_REDIRECT
Fired when the payment is successful and a return URL was provided.
\`\`\`javascript
{
  type: 'PAYMENT_REDIRECT',
  url: 'https://yoursite.com/success'
}
\`\`\`

### PAYMENT_TIMEOUT
Fired when payment processing times out.
\`\`\`javascript
{
  type: 'PAYMENT_TIMEOUT',
  error: 'Payment processing timed out',
  timestamp: '2024-01-01T12:00:00.000Z'
}
\`\`\`

## Error Handling

All API endpoints return consistent error responses:

\`\`\`json
{
  "success": false,
  "error": "Error type",
  "message": "Human-readable error message",
  "details": "Additional error details (optional)"
}
\`\`\`

Common HTTP status codes:
- \`200\`: Success
- \`400\`: Bad Request (validation errors)
- \`401\`: Unauthorized (invalid token)
- \`404\`: Not Found (merchant not found)
- \`413\`: Payload Too Large
- \`500\`: Internal Server Error

## Security Considerations

1. **Token Security**: Tokens are cryptographically secure and single-use
2. **CORS**: Configure allowed origins for production
3. **HTTPS**: Always use HTTPS in production
4. **CSP**: Implement Content Security Policy headers
5. **Input Validation**: All inputs are validated server-side
6. **Rate Limiting**: API endpoints include basic rate limiting
`;
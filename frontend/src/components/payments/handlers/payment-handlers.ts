import { toast } from "sonner";
import { processTokenPayment } from "../../../services/api";
import { PayFieldsConfig, BillingAddress, PaymentResponse, PaymentError } from "../types/payfields.types";
import { postMessageToParent } from "../utils/iframe-communication";

export const createPaymentSuccessHandler = (config: PayFieldsConfig, billingAddress?: BillingAddress, onSuccess?: (response: unknown) => void) => {
  return async (response: PaymentResponse) => {
    console.log("Payment successful:", response);
    console.log("🔍 Debugging PayFields response structure:", {
      responseKeys: Object.keys(response),
      hasToken: !!response.token,
      tokenValue: response.token,
      configMode: config.mode,
      configTxnType: config.txnType,
      configAmount: config.amount,
      fullResponse: response,
    });

    if (config.mode === "token" && response.token) {
      console.log("✅ Token generated successfully:", {
        token: response.token.substring(0, 8) + "...",
        merchantId: config.merchantId,
        amount: config.amount,
        mode: config.mode,
        txnType: config.txnType,
        note: "Token ready for backend processing",
      });

      try {
        const paymentResult = await processTokenPayment({
          merchantId: config.merchantId,
          token: response.token,
          amount: config.amount,
          description: config.description,
          customerInfo: billingAddress
            ? {
                name: `${billingAddress.firstName} ${billingAddress.lastName}`,
                email: billingAddress.email,
                address: {
                  line1: billingAddress.line1,
                  line2: billingAddress.line2,
                  city: billingAddress.city,
                  state: billingAddress.state,
                  zip: billingAddress.zip,
                  country: billingAddress.country,
                },
              }
            : undefined,
        });

        if (paymentResult.success) {
          toast.success("Payment processed successfully!");
          console.log("✅ Token payment completed successfully:", {
            transactionId: paymentResult.transaction?.id,
            status: paymentResult.transaction?.status,
            amount: paymentResult.transaction?.amount,
            merchantId: config.merchantId,
          });

          postMessageToParent("PAYMENT_SUCCESS", {
            data: {
              ...response,
              transaction: paymentResult.transaction,
              merchantInfo: paymentResult.merchantInfo,
              tokenProcessed: true,
            },
          });

          if (onSuccess) {
            onSuccess({
              ...response,
              transaction: paymentResult.transaction,
              merchantInfo: paymentResult.merchantInfo,
              tokenProcessed: true,
            });
          }
        } else {
          throw new Error(paymentResult.message || "Token payment processing failed");
        }
      } catch (error) {
        console.error("❌ Token payment processing failed:", {
          error: error instanceof Error ? error.message : "Unknown error",
          token: response.token?.substring(0, 8) + "...",
          merchantId: config.merchantId,
          amount: config.amount,
        });

        const errorMessage = error instanceof Error ? error.message : "Token payment processing failed";
        toast.error(`Payment failed: ${errorMessage}`);

        postMessageToParent("PAYMENT_ERROR", {
          error: errorMessage,
          tokenGenerated: true,
          tokenProcessingFailed: true,
        });

        if (onSuccess) {
          onSuccess({
            error: errorMessage,
            tokenGenerated: true,
            tokenProcessingFailed: true,
          });
        }
        throw error;
      }
    } else if (config.mode === "token" && !response.token) {
      // Check for alternative token field names in the response
      const possibleTokenFields = ["token", "paymentToken", "authToken", "id"];
      let foundToken = null;

      for (const field of possibleTokenFields) {
        if (response[field]) {
          foundToken = response[field];
          console.log(`🔍 Found token in field '${field}':`, foundToken);
          break;
        }
      }

      // Check if token is nested in data array
      if (!foundToken && response.data && Array.isArray(response.data) && response.data.length > 0) {
        const firstDataItem = response.data[0];
        for (const field of possibleTokenFields) {
          if (firstDataItem[field]) {
            foundToken = firstDataItem[field];
            console.log(`🔍 Found token in data[0].${field}:`, foundToken);
            break;
          }
        }
      }

      if (foundToken) {
        // Process the found token
        console.log("✅ Token found in alternative location, processing...");
        // Update response object with the found token
        response.token = foundToken;
        // Recursively call this handler with the updated response
        return await createPaymentSuccessHandler(config, billingAddress, onSuccess)(response);
      }

      // Token mode but no token received - this is an error
      const errorMessage = "Token generation failed: No token received from PayFields";
      console.error("❌ Token generation failed:", {
        mode: config.mode,
        txnType: config.txnType,
        response,
        merchantId: config.merchantId,
        checkedFields: possibleTokenFields,
      });

      toast.error(errorMessage);
      postMessageToParent("PAYMENT_ERROR", {
        error: errorMessage,
        tokenGenerationFailed: true,
      });

      if (onSuccess) {
        onSuccess({
          error: errorMessage,
          tokenGenerationFailed: true,
        });
      }
      throw new Error(errorMessage);
    } else {
      // Non-token mode or successful processing
      toast.success("Payment processed successfully!");
      postMessageToParent("PAYMENT_SUCCESS", { data: response });
      if (onSuccess) onSuccess(response);
    }
  };
};

export const createPaymentFailureHandler = (onFailure?: (error: unknown) => void) => {
  return (err: PaymentError) => {
    console.error("Payment failed:", err);

    let errorMessage = "Payment processing failed. Please try again.";
    if (err && Array.isArray(err.errors)) {
      const fieldErrors = err.errors.map((e) => `${e.field}: ${e.msg}`).join(", ");
      errorMessage = `Payment failed: ${fieldErrors}`;
    } else if (err && err.message) {
      errorMessage = err.message;
    }

    toast.error(errorMessage);
    postMessageToParent("PAYMENT_FAILURE", {
      error: errorMessage,
      details: err,
    });

    if (onFailure) onFailure(err);
  };
};

export const createValidationFailureHandler = (onFailure?: (error: unknown) => void) => {
  return (err: unknown) => {
    console.log("Validation error:", err);

    const validationMessage = "Payment validation failed. Please check your card details.";
    toast.error("Please check your card details");

    postMessageToParent("PAYMENT_VALIDATION_FAILURE", {
      error: validationMessage,
      details: err,
    });

    if (onFailure) onFailure({ message: validationMessage, details: err });
  };
};

export const createPaymentFinishHandler = () => {
  return (response: unknown) => {
    console.log("PayFields finished:", response);
    postMessageToParent("PAYMENT_FINISHED", { data: response });
  };
};

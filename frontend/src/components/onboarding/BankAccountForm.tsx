import { useDispatch, useSelector } from "react-redux";
import { type RootState } from "../../redux/store.ts";
import { updateFormData, nextStep, prevStep } from "../../redux/slices/onboardingSlice.ts";
import { useState } from "react";
import { toast } from "sonner";
import { BankVerificationSection, BankAccountTermsSection, BankAccountInfoSection } from "./sections";
import { validateBankAccount, type BankAccountValidationErrors } from "./utils/validation";
import { VERIFICATION_METHODS, type VerificationMethod } from "./constants/bankAccountConstants";
import { createNote, createNoteDocument } from "../../services/merchants/api";
import { PlaidAccountData } from "../../hooks/usePlaidLink";

const BankAccountForm = () => {
  const dispatch = useDispatch();
  const { formData } = useSelector((state: RootState) => state.onboarding);
  const [errors, setErrors] = useState<BankAccountValidationErrors>({});
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [verificationMethod, setVerificationMethod] = useState<VerificationMethod>(VERIFICATION_METHODS.MANUAL);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [uploadPreview, setUploadPreview] = useState<string | null>(null);
  const [plaidAccountData, setPlaidAccountData] = useState<PlaidAccountData | null>(null);

  const isSoleProprietor = formData.type === 1;
  const account = formData.accounts?.[0] || {
    primary: 1,
    account: {
      method: isSoleProprietor ? 8 : 10,
      number: "",
      routing: "",
    },
  };

  const handlePlaidSuccess = (accountData: PlaidAccountData) => {
    setPlaidAccountData(accountData);
    toast.success("Bank account connected successfully!", {
      description: `Connected ${accountData.institutionName} account ending in ${accountData.accountMask}`,
    });
  };

  const validateForm = () => {
    const validationErrors = validateBankAccount(account, termsAccepted, verificationMethod, uploadedFile, plaidAccountData);
    setErrors(validationErrors);

    if (Object.keys(validationErrors).length > 0) {
      toast.error("Please fix the following errors:", {
        description: Object.values(validationErrors).join(", "),
      });
      return false;
    }
    return true;
  };

  const handleVerificationUpload = async (entityId: string): Promise<boolean> => {
    if (verificationMethod !== VERIFICATION_METHODS.MANUAL || !uploadedFile) {
      return true;
    }

    try {
      toast.info("Uploading verification document...");

      // Step 1: Create a note
      const noteResponse = await createNote({
        entityId,
        note: "Bank account verification - void check uploaded",
        type: "bank_verification",
      });

      if (!noteResponse.success) {
        throw new Error(noteResponse.message || "Failed to create note");
      }

      // Step 2: Create note document
      const documentResponse = await createNoteDocument({
        noteId: noteResponse.data.noteId,
        file: uploadedFile,
        description: "Void check for bank account verification",
      });

      if (!documentResponse.success) {
        throw new Error(documentResponse.message || "Failed to upload document");
      }

      toast.success("Verification document uploaded successfully");
      return true;
    } catch (error) {
      console.error("Error uploading verification document:", error);

      let errorMessage = "Failed to upload verification document. Please try again.";

      if (error instanceof Error) {
        // Check for specific error types
        if (error.message.includes("File must be")) {
          errorMessage = error.message;
        } else if (error.message.includes("File size")) {
          errorMessage = "File size exceeds 10MB limit. Please choose a smaller file.";
        } else if (error.message.includes("network") || error.message.includes("timeout")) {
          errorMessage = "Network error. Please check your connection and try again.";
        } else if (error.message.includes("Invalid token") || error.message.includes("authentication")) {
          errorMessage = "Authentication error. Please refresh the page and try again.";
        }
      }

      toast.error(errorMessage);
      return false;
    }
  };

  // Suppress unused variable warning - this function will be used in future implementation
  void handleVerificationUpload;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      // Store verification method and file info in form data for later use
      const verificationData: {
        verificationMethod: VerificationMethod;
        verificationFile?: {
          name: string;
          size: number;
          type: string;
          content: string;
        };
        plaidData?: {
          publicToken: string;
          accountToken: string;
          platform: "PLAID";
          institutionName?: string;
          accountName?: string;
          accountMask?: string;
        };
      } = {
        verificationMethod,
      };

      // If manual verification with file, convert file to base64
      if (verificationMethod === VERIFICATION_METHODS.MANUAL && uploadedFile) {
        try {
          const fileContent = await convertFileToBase64(uploadedFile);
          verificationData.verificationFile = {
            name: uploadedFile.name,
            size: uploadedFile.size,
            type: uploadedFile.type,
            content: fileContent,
          };
        } catch (error) {
          console.error("Error converting file to base64:", error);
          toast.error("Failed to process file. Please try again.");
          return;
        }
      }

      // If Plaid verification, store Plaid data
      if (verificationMethod === VERIFICATION_METHODS.PLAID && plaidAccountData) {
        verificationData.plaidData = {
          publicToken: plaidAccountData.publicToken,
          accountToken: plaidAccountData.accountToken,
          platform: "PLAID",
          institutionName: plaidAccountData.institutionName,
          accountName: plaidAccountData.accountName,
          accountMask: plaidAccountData.accountMask,
        };
      }

      dispatch(updateFormData({ bankVerification: verificationData }));
      dispatch(nextStep());
    }
  };

  const convertFileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data URL prefix (e.g., "data:image/png;base64,")
        const base64Content = result.split(",")[1];
        resolve(base64Content);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };

  const handleChange = (field: string, value: string | number) => {
    const updatedAccount = field.startsWith("account.")
      ? {
          ...account,
          currency: "USD",
          account: { ...account.account, [field.replace("account.", "")]: value },
        }
      : { ...account, currency: "USD", [field]: value };
    dispatch(updateFormData({ accounts: [updatedAccount] }));
  };

  const handleFileChange = (file: File | null) => {
    setUploadedFile(file);
    if (errors.voidCheck && file) {
      setErrors({ ...errors, voidCheck: undefined });
    }
  };

  const handlePreviewChange = (preview: string | null) => {
    setUploadPreview(preview);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="border-b border-gray-200 px-8 py-6">
            <h1 className="text-2xl font-semibold text-gray-900">Bank Account Information</h1>
            <p className="text-gray-600 mt-1">Enter your business bank account details for payment processing</p>
          </div>

          <form onSubmit={handleSubmit} className="px-8 py-8">
            <BankVerificationSection
              verificationMethod={verificationMethod}
              onVerificationMethodChange={setVerificationMethod}
              uploadedFile={uploadedFile}
              onFileChange={handleFileChange}
              uploadPreview={uploadPreview}
              onPreviewChange={handlePreviewChange}
              errors={errors}
              onPlaidSuccess={handlePlaidSuccess}
              account={account}
              onChange={handleChange}
              isSoleProprietor={isSoleProprietor}
            />

            <BankAccountInfoSection />

            <BankAccountTermsSection termsAccepted={termsAccepted} onTermsChange={setTermsAccepted} errors={errors} />

            <div className="border-t border-gray-200 pt-6 flex justify-between">
              <button
                type="button"
                onClick={() => dispatch(prevStep())}
                className="bg-gray-100 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-200 focus:ring-4 focus:ring-gray-200 transition-all duration-200"
              >
                Previous
              </button>
              <button
                type="submit"
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-all duration-200"
              >
                Review & Submit
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default BankAccountForm;
